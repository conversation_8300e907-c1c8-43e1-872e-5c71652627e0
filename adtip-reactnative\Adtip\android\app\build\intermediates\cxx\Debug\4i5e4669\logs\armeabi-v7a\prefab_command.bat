@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "F:\\R17DevTools\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging898141025089513619\\staged-cli-output" ^
  "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-vision-camera\\145q2r3k" ^
  "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab" ^
  "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\refs\\shopify_react-native-skia\\4b6j3y4k" ^
  "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\q1x4i2n1" ^
  "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\b6a769c22a825b14c29ec2d96d2172b0\\transformed\\jetified-hermes-android-0.79.2-debug\\prefab" ^
  "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab"
