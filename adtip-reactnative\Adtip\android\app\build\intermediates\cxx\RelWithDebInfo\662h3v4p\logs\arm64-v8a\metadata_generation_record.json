[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a\\android_gradle_build.json due to:", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Java\\\\jdk-17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"F:\\\\R17DevTools\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging5983612795093217884\\\\staged-cli-output\" ^\n  \"F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-vision-camera\\\\145q2r3k\" ^\n  \"F:\\\\R17DevTools\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\0dbfeb2f307611649a8567893eebd290\\\\transformed\\\\jetified-react-android-0.79.2-release\\\\prefab\" ^\n  \"F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\shopify_react-native-skia\\\\4b6j3y4k\" ^\n  \"F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\q1x4i2n1\" ^\n  \"F:\\\\R17DevTools\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\b72e92ab978b0513249f06adacda07ce\\\\transformed\\\\jetified-hermes-android-0.79.2-release\\\\prefab\" ^\n  \"F:\\\\R17DevTools\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\8d81b0c8ae21d76d183ae0c44210c625\\\\transformed\\\\jetified-fbjni-0.7.0\\\\prefab\"\n", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a'", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a'", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BF:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HF:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=F:\\\\R17DevTools\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BF:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\.cxx\\\\RelWithDebInfo\\\\662h3v4p\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=F:\\\\A1\\\\adtip-reactnative\\\\Adtip\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a\\compile_commands.json.bin normally", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libc++_shared.so in incremental regenerate", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libfbjni.so in incremental regenerate", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libjsi.so in incremental regenerate", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "deleted unexpected build output F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreactnative.so in incremental regenerate", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a\\compile_commands.json to F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\tools\\release\\arm64-v8a\\compile_commands.json", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]