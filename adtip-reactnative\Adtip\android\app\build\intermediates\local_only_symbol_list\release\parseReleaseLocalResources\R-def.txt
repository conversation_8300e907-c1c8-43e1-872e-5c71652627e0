R_DEF: Internal format may change without notice
local
color backgroundColor
color black
color borderColor
color call_background
color call_blue
color call_green
color call_orange
color call_overlay
color call_red
color cardBackground
color colorAccent
color colorPrimary
color colorPrimaryDark
color iconColor
color red
color rippleColor
color statusBarColor
color textPrimary
color textSecondary
color textTertiary
color transparent
color white
drawable assets_images_logo
drawable ic_call
drawable ic_call_end
drawable ic_launcher_background
drawable ic_mic_off
drawable ic_volume_up
drawable node_modules_cpxresearchsdkreactnative_assets_clock
drawable node_modules_cpxresearchsdkreactnative_assets_close
drawable node_modules_cpxresearchsdkreactnative_assets_help
drawable node_modules_cpxresearchsdkreactnative_assets_home
drawable node_modules_cpxresearchsdkreactnative_assets_settings
drawable node_modules_cpxresearchsdkreactnative_assets_star
drawable node_modules_reactnavigation_elements_lib_module_assets_backicon
drawable node_modules_reactnavigation_elements_lib_module_assets_backiconmask
drawable node_modules_reactnavigation_elements_lib_module_assets_clearicon
drawable node_modules_reactnavigation_elements_lib_module_assets_closeicon
drawable node_modules_reactnavigation_elements_lib_module_assets_searchicon
drawable rn_edit_text_material
drawable src_assets_gifs_congratulation
drawable src_assets_gpayicon_icons8googlepay48
drawable src_assets_images_default_profile
drawable src_assets_images_defaultavatar
drawable src_assets_images_logo
drawable src_assets_images_onboarding1
drawable src_assets_images_onboarding2
drawable src_assets_images_onboarding3
drawable src_assets_phonepeicon_icons8phonepe48
drawable src_assets_upi_icon_upi
id answer_button
id call_type
id caller_name
id decline_button
integer react_native_dev_server_port
layout activity_incoming_call
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string app_name
string com.google.firebase.crashlytics.mapping_file_id
string default_web_client_id
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string project_id
style AppTheme
