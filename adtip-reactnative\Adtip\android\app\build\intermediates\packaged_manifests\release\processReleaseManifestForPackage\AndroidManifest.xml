<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.adtip.app.adtip_app"
    android:versionCode="30002"
    android:versionName="31.0.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- BASIC NETWORK PERMISSIONS -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- WHATSAPP-LIKE CALLING PERMISSIONS - Minimal set -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />

    <!-- VIDEOSDK & OVERLAY PERMISSIONS -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- BLUETOOTH PERMISSIONS -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />

    <!-- LOCATION PERMISSIONS -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- STORAGE PERMISSIONS -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- NOTIFICATIONS -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />

    <!-- CALLLEEP SELF-MANAGED PERMISSIONS -->
    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <!-- ADD_VOICEMAIL removed - not used by core app functionality -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <!-- Camera features -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />

    <!-- AD TRACKING -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        android:maxSdkVersion="29" />

    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:mimeType="*/*"
                android:scheme="*" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data
                android:host="pay"
                android:mimeType="*/*"
                android:scheme="upi" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SEND" />

            <data android:mimeType="*/*" />
        </intent>
        <intent>
            <action android:name="rzp.device_token.share" />
        </intent> <!-- For browser content -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent> <!-- End of browser content -->
        <!-- For CustomTabsService -->
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent> <!-- End of CustomTabsService -->
        <!-- For MRAID capabilities -->
        <intent>
            <action android:name="android.intent.action.INSERT" />

            <data android:mimeType="vnd.android.cursor.dir/event" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="sms" />
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />

            <data android:path="tel:" />
        </intent>
        <intent>
            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
        </intent>
    </queries>

    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission
        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
        android:maxSdkVersion="30" /> <!-- For Xiaomi devices to enable heads-up notifications as default (https://github.com/invertase/notifee/issues/296) -->
    <uses-permission
        android:name="android.permission.ACCESS_NOTIFICATION_POLICY"
        android:minSdkVersion="23" />

    <application
        android:name="com.adtip.app.adtip_app.MainApplication"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true" >
        <meta-data
            android:name="android.app.extract_native_libs"
            android:value="true" />

        <!-- AdMob App ID - PubScale Only -->
        <!-- Note: Simplified ad system uses only PubScale ad units -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3206456546664189~6654042212" />

        <!-- VIDEOSDK FOREGROUND SERVICE CONFIGURATION -->
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_channel_name"
            android:value="Meeting Notification" />
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_channel_description"
            android:value="Whenever meeting started notification will appear." />
        <meta-data
            android:name="live.videosdk.rnfgservice.notification_color"
            android:resource="@android:color/holo_red_dark" />

        <!-- Firebase messaging default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="adtip_call_channel" />

        <activity
            android:name="com.adtip.app.adtip_app.MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:showWhenLocked="true"
            android:turnScreenOn="true"
            android:windowSoftInputMode="adjustPan" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Custom URL Scheme Deep Links -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="adtip" />
            </intent-filter>

            <!-- Universal Links / App Links for adtip.in -->
            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="adtip.in"
                    android:scheme="https" />
            </intent-filter>

            <!-- Universal Links for www.adtip.in -->
            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.adtip.in"
                    android:scheme="https" />
            </intent-filter>

            <!-- Universal Links for app.adtip.in -->
            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app.adtip.in"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="adtip.com" />
            </intent-filter>
        </activity>

        <!-- VIDEOSDK FOREGROUND SERVICE -->
        <service
            android:name="live.videosdk.rnfgservice.ForegroundService"
            android:exported="false"
            android:foregroundServiceType="camera|microphone" />

        <!-- Notifee's foreground service declaration for camera and microphone -->
        <service
            android:name="app.notifee.core.ForegroundService"
            android:exported="false"
            android:foregroundServiceType="camera|microphone|phoneCall|shortService" />

        <!-- CALLLEEP CONNECTION SERVICE -->
        <service
            android:name="io.wazo.callkeep.VoiceConnectionService"
            android:exported="true"
            android:foregroundServiceType="phoneCall|camera|microphone"
            android:label="VoiceConnectionService"
            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE" >
            <intent-filter>
                <action android:name="android.telecom.ConnectionService" />
            </intent-filter>

            <meta-data
                android:name="android.telecom.CONNECTION_SERVICE"
                android:value="android.telecom.ConnectionService" />
        </service>
        <service android:name="io.wazo.callkeep.RNCallKeepBackgroundMessagingService" />

        <!-- ADTIP FIREBASE MESSAGING SERVICE -->
        <service
            android:name="com.adtip.app.adtip_app.AdtipFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- ADTIP CALL RINGING SERVICE -->
        <service
            android:name="com.adtip.app.adtip_app.CallRingingService"
            android:exported="false" />

        <meta-data
            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
            android:value="false" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />

        <provider
            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
            android:authorities="com.adtip.app.adtip_app.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
        <provider
            android:name="io.invertase.notifee.NotifeeInitProvider"
            android:authorities="com.adtip.app.adtip_app.notifee-init-provider"
            android:exported="false"
            android:initOrder="-100" />

        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="firebase_analytics_collection_deactivated"
            android:value="false" />
        <meta-data
            android:name="google_analytics_adid_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="google_analytics_ssaid_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="google_analytics_automatic_screen_reporting_enabled"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_analytics_storage"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_storage"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_user_data"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_personalization_signals"
            android:value="true" /> <!-- Disable crashlytics by default so we can custom init with CrashlyticsNdk support -->
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />

        <provider
            android:name="io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider"
            android:authorities="com.adtip.app.adtip_app.reactnativefirebasecrashlyticsinitprovider"
            android:exported="false"
            android:initOrder="98" />

        <service
            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
            android:exported="false" />
        <service
            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver
            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="delivery_metrics_exported_to_big_query_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="true" />
        <meta-data
            android:name="firebase_messaging_notification_delegation_enabled"
            android:value="false" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/white" />
        <meta-data
            android:name="app_data_collection_default_enabled"
            android:value="true" />

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
            android:authorities="com.adtip.app.adtip_app.reactnativefirebaseappinitprovider"
            android:exported="false"
            android:initOrder="99" />
        <provider
            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
            android:authorities="com.adtip.app.adtip_app.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
        </provider>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" /> <!-- Prompt Google Play services to install the backported photo picker module -->
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>

            <meta-data
                android:name="photopicker_activity:0:required"
                android:value="" />
        </service>

        <provider
            android:name="com.imagepicker.ImagePickerProvider"
            android:authorities="com.adtip.app.adtip_app.imagepickerprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/imagepicker_provider_paths" />
        </provider>

        <activity
            android:name="com.razorpay.CheckoutActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:theme="@style/CheckoutTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.pubscale.sdkone.offerwall.ui.OfferWallActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/Theme.PubScaleOfferWallSDK" />

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.adtip.app.adtip_app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="com.razorpay.RazorpayInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="com.razorpay.MagicXActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.razorpay.plugin.googlepay_all"
            android:value="com.razorpay.RzpGpayMerged" />

        <activity
            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="genericidp" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="recaptcha" />
            </intent-filter>
        </activity>

        <service
            android:name="com.google.firebase.sessions.SessionLifecycleService"
            android:enabled="true"
            android:exported="false" />

        <meta-data
            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
            android:value="GlideModule" />

        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.adtip.app.adtip_app.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
            android:authorities="com.adtip.app.adtip_app.mobileadsinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="com.google.android.gms.ads.AdService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false" />
        <activity
            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
        <service
            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
            android:enabled="true"
            android:exported="false" >
            <meta-data
                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
        </service>

        <activity
            android:name="androidx.credentials.playservices.HiddenActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:enabled="true"
            android:exported="false"
            android:fitsSystemWindows="true"
            android:theme="@style/Theme.Hidden" >
        </activity>

        <uses-library
            android:name="androidx.camera.extensions.impl"
            android:required="false" />

        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>

        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />

        <meta-data
            android:name="com.bumptech.glide.integration.webp.WebpGlideModule"
            android:value="GlideModule" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" /> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.adtip.app.adtip_app.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" /> <!-- Receiver Service -->
        <service
            android:name="app.notifee.core.ReceiverService"
            android:exported="false" />

        <activity
            android:name="app.notifee.core.NotificationReceiverActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:noHistory="true"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name="app.notifee.core.RebootBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="app.notifee.core.AlarmPermissionBroadcastReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="app.notifee.core.NotificationAlarmReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver> <!-- Broadcast Receiver -->
        <receiver
            android:name="app.notifee.core.BlockStateBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.app.action.APP_BLOCK_STATE_CHANGED" />
                <action android:name="android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED" />
                <action android:name="android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.jakewharton.processphoenix.ProcessPhoenix"
            android:exported="false"
            android:process=":phoenix"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.facebook.soloader.enabled"
            android:value="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
        <activity
            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
            android:exported="false"
            android:stateNotNeeded="true"
            android:theme="@style/Theme.PlayCore.Transparent" />
    </application>

</manifest>