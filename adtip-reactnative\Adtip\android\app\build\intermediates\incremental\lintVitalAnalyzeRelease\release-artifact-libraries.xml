<libraries>
  <library
      name=":@@:react-native-community_datetimepicker::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4b813ae2a1a7e46bcce59e2186218f27\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\4b813ae2a1a7e46bcce59e2186218f27\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-community_datetimepicker:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\datetimepicker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4b813ae2a1a7e46bcce59e2186218f27\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-google-mobile-ads::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ed9ff4aef6316531b15e20d711dbdb1f\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\ed9ff4aef6316531b15e20d711dbdb1f\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-google-mobile-ads:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ed9ff4aef6316531b15e20d711dbdb1f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-permissions::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\61bb189215aa158bbfa20e65686d5bbb\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\61bb189215aa158bbfa20e65686d5bbb\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-permissions:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-permissions\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\61bb189215aa158bbfa20e65686d5bbb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-screens::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dc40a4701279d8fc6106534f9dbbde69\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\dc40a4701279d8fc6106534f9dbbde69\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-screens:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-screens\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dc40a4701279d8fc6106534f9dbbde69\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-video::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\76342bf04c757469c5912d3d274e034c\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\76342bf04c757469c5912d3d274e034c\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-video:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\76342bf04c757469c5912d3d274e034c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-vision-camera::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\53ec3e3dbcc8ff73da7131eb933d6857\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\53ec3e3dbcc8ff73da7131eb933d6857\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-vision-camera:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-vision-camera\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\53ec3e3dbcc8ff73da7131eb933d6857\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:notifee_react-native::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8f4aa591ef867ec84cb55c7a0142fecb\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\8f4aa591ef867ec84cb55c7a0142fecb\transformed\out\jars\libs\R.jar"
      resolved="Adtip:notifee_react-native:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8f4aa591ef867ec84cb55c7a0142fecb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_analytics::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b2b6fa1f85d377d22bb57194f08e936a\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\b2b6fa1f85d377d22bb57194f08e936a\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_analytics:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b2b6fa1f85d377d22bb57194f08e936a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_auth::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\bc6fe4e63ab97e23f7b355348416a807\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\bc6fe4e63ab97e23f7b355348416a807\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_auth:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\auth\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\bc6fe4e63ab97e23f7b355348416a807\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_crashlytics::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0d6ed245a119512ca16dda7464af4b93\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\0d6ed245a119512ca16dda7464af4b93\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_crashlytics:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0d6ed245a119512ca16dda7464af4b93\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_database::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b39ec4910ef82b5831e095926b74f972\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\b39ec4910ef82b5831e095926b74f972\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_database:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\database\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b39ec4910ef82b5831e095926b74f972\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_firestore::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\320125e72ead92fbb1af4c84e797325a\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\320125e72ead92fbb1af4c84e797325a\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_firestore:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\firestore\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\320125e72ead92fbb1af4c84e797325a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_messaging::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dfb1cb322612bfafaa1ca8e31754e796\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\dfb1cb322612bfafaa1ca8e31754e796\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_messaging:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dfb1cb322612bfafaa1ca8e31754e796\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_storage::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e00cbe6b35d84a1cad9cb1836cf91358\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\e00cbe6b35d84a1cad9cb1836cf91358\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_storage:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\storage\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e00cbe6b35d84a1cad9cb1836cf91358\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-firebase_app::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b4ae56114f044130a7dc6cc7e4392470\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\b4ae56114f044130a7dc6cc7e4392470\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-firebase_app:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b4ae56114f044130a7dc6cc7e4392470\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-pager-view::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\1468dfa35c3e37dd79327609762021a1\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\1468dfa35c3e37dd79327609762021a1\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-pager-view:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-pager-view\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\1468dfa35c3e37dd79327609762021a1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-restart::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\65276119075d6ae6d2429945a60b3ba1\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\65276119075d6ae6d2429945a60b3ba1\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-restart:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-restart\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\65276119075d6ae6d2429945a60b3ba1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:react-android:0.79.2:release@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0dbfeb2f307611649a8567893eebd290\transformed\jetified-react-android-0.79.2-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.79.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0dbfeb2f307611649a8567893eebd290\transformed\jetified-react-android-0.79.2-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.pubscale.sdkone:offerwall:1.0.11@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\jars\classes.jar"
      resolved="com.pubscale.sdkone:offerwall:1.0.11"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\99abf5a50fc98d18cb635d28e1940775\transformed\jetified-fresco-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\99abf5a50fc98d18cb635d28e1940775\transformed\jetified-fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\78ac3fda40d25e87ec6da10f5419cce4\transformed\jetified-imagepipeline-okhttp3-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\78ac3fda40d25e87ec6da10f5419cce4\transformed\jetified-imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\970de0b2c371c7cf0f9620187162923c\transformed\jetified-middleware-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\970de0b2c371c7cf0f9620187162923c\transformed\jetified-middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f1c2ca7d50378944176dce7a22e614a2\transformed\jetified-ui-common-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f1c2ca7d50378944176dce7a22e614a2\transformed\jetified-ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\04861295927f29f4c31729c0480972d1\transformed\jetified-firebase-analytics-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d8f3cb842f708b4d9f96a19d0863fec6\transformed\jetified-appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ef18ad19ff26599d64ec0eff4ea7dc70\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d782996d2c63197f7994f271ec433e72\transformed\jetified-user-messaging-platform-3.2.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d782996d2c63197f7994f271ec433e72\transformed\jetified-user-messaging-platform-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9ff25ec0ffb4ba03051c851e4be9c2cb\transformed\jetified-play-services-measurement-sdk-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ae7731337058eb9eda9e6d21a8139142\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:20.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\40721e3c98568ef24c2bd2c8723a65bb\transformed\jetified-firebase-measurement-connector-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c3d93af0b117ed25b95c723ac599fee1\transformed\jetified-play-services-measurement-impl-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8e96fdf56052633f58269012fd055a27\transformed\jetified-play-services-ads-identifier-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e34b30e2c3807979c179fdca07abbb5d\transformed\jetified-play-services-measurement-sdk-api-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\39b735ae1272dec9f6411321da8caea6\transformed\jetified-play-services-measurement-base-22.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\427783aa1196df4b1bb2539d88538822\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2f6f51f16e3e4bdb0e2c4a9a46c82ae1\transformed\jetified-firebase-installations-interop-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2f6f51f16e3e4bdb0e2c4a9a46c82ae1\transformed\jetified-firebase-installations-interop-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8df680b809f8e7d220078d7456e06bb5\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8df680b809f8e7d220078d7456e06bb5\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\42ae8010e5b4e61eec06f059aac62005\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\42ae8010e5b4e61eec06f059aac62005\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\62bfd621dfe7196adbe33c0a81a67cb9\transformed\swiperefreshlayout-1.2.0-alpha01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ca18fac9e39387f0d5d0e3040d168e14\transformed\jetified-exoplayer-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ca18fac9e39387f0d5d0e3040d168e14\transformed\jetified-exoplayer-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f9b6829f7f71dcf7ed8c3a43ef2febe5\transformed\jetified-exoplayer-ui-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0\jars\classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\84b7b03ef8446d1de9b5d4ea3cda92f6\transformed\media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c9effbe9a9224ae8f0b56f94b90e2693\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f3545518f142d90ae77eddda44b88804\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f3545518f142d90ae77eddda44b88804\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4df02badba841aee3ca4782d345d696b\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4df02badba841aee3ca4782d345d696b\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b6209f910c1bc01c6bfd83d02a0a0d1e\transformed\jetified-core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a38d6d1afe6220c365cd79513855b8ab\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a38d6d1afe6220c365cd79513855b8ab\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\197e971f80ea5cde1b7a09cab8fd2647\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\197e971f80ea5cde1b7a09cab8fd2647\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ca7051459246db1c67315c146b3cbc24\transformed\lifecycle-viewmodel-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ca7051459246db1c67315c146b3cbc24\transformed\lifecycle-viewmodel-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\fb667b71c56725e04f12fbd8df400c00\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\cd47cb17e395838b1ad8102f427d6548\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.7@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.7\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.7.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.7"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c9708445cb3f39759eddbf7eb8a24dfa\transformed\lifecycle-livedata-core-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a8feaa743bf52883b5bccbe748b774d2\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.9.0\6cf3b8df5a2ce8c6b0dffe55657a827c405ee2fe\kotlinx-coroutines-android-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a74c60921ee11cceb07fcadaf42c9d30\transformed\jetified-ads-adservices-java-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.9.0\9beade4c1c1569e4f36cbd2c37e02e3e41502601\kotlinx-coroutines-core-jvm-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.9.0\9d715295c29dddf8c381565c513fb2d9923f9f41\kotlinx-coroutines-play-services-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9377c5ad5cc8b4838d355755ff999044\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.5.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.5.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\1011d3940cd0ae6659aab4410e8bea7d\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b0d63cb00ac6bfd545ea575ceef8be11\transformed\jetified-activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dc4429a347bd69bf2e56511963168424\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\72f2f97f1edd16238c17e264fdaf1dad\transformed\jetified-fbcore-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\72f2f97f1edd16238c17e264fdaf1dad\transformed\jetified-fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\091d030d0ecfc4218a289f6c4f9c847f\transformed\jetified-drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\eabb388f0f5b75242e6f4e74de536f0e\transformed\jetified-firebase-encoders-json-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\eabb388f0f5b75242e6f4e74de536f0e\transformed\jetified-firebase-encoders-json-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6fad05167c4309eb9d3d503e9503016a\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6fad05167c4309eb9d3d503e9503016a\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3c03edf9c7d4eeed7a5d0ced934b7ec1\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3c03edf9c7d4eeed7a5d0ced934b7ec1\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c33465fa9d621cd2aab064bc43ec165a\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\32d5b301c4730608d020d9e165acf3e1\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\32d5b301c4730608d020d9e165acf3e1\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7b3d19fb1cf6c0d27c59fcb1b19cd6b9\transformed\localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\97947d976178ed08dcaea2b05ec1bb80\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\97947d976178ed08dcaea2b05ec1bb80\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\db21677b3a01819e80addb86a0500cda\transformed\jetified-core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2bb283f821c52533d234058e0f2b4463\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\cb4eab3a116796f2406261ab34aeaf50\transformed\jetified-ui-core-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\cb4eab3a116796f2406261ab34aeaf50\transformed\jetified-ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\90d722366856612632b7013042974af8\transformed\jetified-imagepipeline-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\90d722366856612632b7013042974af8\transformed\jetified-imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9d14d9da48e39ed27a64484686b573e1\transformed\jetified-imagepipeline-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9d14d9da48e39ed27a64484686b573e1\transformed\jetified-imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.21\222b2be42672d47c002c1b22ac9f030d781fc5db\kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:d11_react-native-fast-image::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7bc3bc23d5f6bf2256145ab252058225\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\7bc3bc23d5f6bf2256145ab252058225\transformed\out\jars\libs\R.jar"
      resolved="Adtip:d11_react-native-fast-image:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@d11\react-native-fast-image\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7bc3bc23d5f6bf2256145ab252058225\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:nozbe_watermelondb::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\421e674d6de79b274eff6f92179e94db\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\421e674d6de79b274eff6f92179e94db\transformed\out\jars\libs\R.jar"
      resolved="Adtip:nozbe_watermelondb:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@nozbe\watermelondb\native\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\421e674d6de79b274eff6f92179e94db\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\180f926e023ef96a8f46b47ac27d3eb9\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\180f926e023ef96a8f46b47ac27d3eb9\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-async-storage_async-storage:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\180f926e023ef96a8f46b47ac27d3eb9\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-clipboard_clipboard::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7c727148c242446aa607b4cf008bec5b\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\7c727148c242446aa607b4cf008bec5b\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-clipboard_clipboard:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-clipboard\clipboard\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7c727148c242446aa607b4cf008bec5b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-community_blur::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f1437413db230f4ddd201e99fff10c88\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\f1437413db230f4ddd201e99fff10c88\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-community_blur:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\blur\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f1437413db230f4ddd201e99fff10c88\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-community_masked-view::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a9ad0aadf7d641af3e680bf07c9e7560\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\a9ad0aadf7d641af3e680bf07c9e7560\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-community_masked-view:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\masked-view\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a9ad0aadf7d641af3e680bf07c9e7560\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-community_netinfo::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\378c561d308a6ef20e4511339853975f\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\378c561d308a6ef20e4511339853975f\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-community_netinfo:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-community\netinfo\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\378c561d308a6ef20e4511339853975f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shopify_react-native-skia::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b1e6283056d79c6988abc43b22dac28f\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\b1e6283056d79c6988abc43b22dac28f\transformed\out\jars\libs\R.jar"
      resolved="Adtip:shopify_react-native-skia:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@shopify\react-native-skia\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b1e6283056d79c6988abc43b22dac28f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:videosdk.live_react-native-webrtc::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4e7182e78ae5680b77668b5afca37fc0\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\4e7182e78ae5680b77668b5afca37fc0\transformed\out\jars\libs\R.jar"
      resolved="Adtip:videosdk.live_react-native-webrtc:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-webrtc\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4e7182e78ae5680b77668b5afca37fc0\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:lottie-react-native::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ac9939fe5f1c92dc4c8db85ddd31bfee\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\ac9939fe5f1c92dc4c8db85ddd31bfee\transformed\out\jars\libs\R.jar"
      resolved="Adtip:lottie-react-native:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\lottie-react-native\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ac9939fe5f1c92dc4c8db85ddd31bfee\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-callkeep::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\cdc545218d842003483bcdd9748db66a\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\cdc545218d842003483bcdd9748db66a\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-callkeep:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\cdc545218d842003483bcdd9748db66a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-compressor::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\94aaeaaa43dd7a1653cd84c7d61f2ec5\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\94aaeaaa43dd7a1653cd84c7d61f2ec5\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-compressor:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-compressor\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\94aaeaaa43dd7a1653cd84c7d61f2ec5\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-create-thumbnail::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8545a76afe86fc9f20d79390cb32267d\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\8545a76afe86fc9f20d79390cb32267d\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-create-thumbnail:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-create-thumbnail\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8545a76afe86fc9f20d79390cb32267d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-date-picker::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\12885175f1ab3d81ce3ac9021ad1b334\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\12885175f1ab3d81ce3ac9021ad1b334\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-date-picker:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-date-picker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\12885175f1ab3d81ce3ac9021ad1b334\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-fs::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\10814a91b18e944cbd6baae5b1b50a68\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\10814a91b18e944cbd6baae5b1b50a68\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-fs:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-fs\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\10814a91b18e944cbd6baae5b1b50a68\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-geolocation-service::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7612b84b2969e60d3b8873d4d5f3cfa0\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\7612b84b2969e60d3b8873d4d5f3cfa0\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-geolocation-service:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-geolocation-service\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7612b84b2969e60d3b8873d4d5f3cfa0\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\39ba123a13fd60d588d0fe8587e112eb\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\39ba123a13fd60d588d0fe8587e112eb\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-gesture-handler:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-gesture-handler\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\39ba123a13fd60d588d0fe8587e112eb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-get-random-values::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\de6b1c33e4237fa3dda4e002f22e819d\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\de6b1c33e4237fa3dda4e002f22e819d\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-get-random-values:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-get-random-values\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\de6b1c33e4237fa3dda4e002f22e819d\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-image-crop-picker::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\fe3f7b5d262d91c6528b94d761a0a989\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\fe3f7b5d262d91c6528b94d761a0a989\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-image-crop-picker:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\fe3f7b5d262d91c6528b94d761a0a989\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-image-picker::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\898893b75c88b35d56da6f9f93053747\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\898893b75c88b35d56da6f9f93053747\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-image-picker:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\898893b75c88b35d56da6f9f93053747\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-linear-gradient::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\083e05f280d115349225d52c1767b0da\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\083e05f280d115349225d52c1767b0da\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-linear-gradient:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-linear-gradient\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\083e05f280d115349225d52c1767b0da\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-orientation-locker::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0c3a2f2167b88d79fc54f7179a6e506a\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\0c3a2f2167b88d79fc54f7179a6e506a\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-orientation-locker:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-orientation-locker\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0c3a2f2167b88d79fc54f7179a6e506a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-razorpay::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a15c17846a8a893c5919a0d58e0d3213\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\a15c17846a8a893c5919a0d58e0d3213\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-razorpay:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a15c17846a8a893c5919a0d58e0d3213\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-reanimated::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0f379aa1a001ab68e92aa8d9c18a2e68\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\0f379aa1a001ab68e92aa8d9c18a2e68\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-reanimated:3.18.0"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-reanimated\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0f379aa1a001ab68e92aa8d9c18a2e68\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b0c7de0182de561e6c7456072c7887a6\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\b0c7de0182de561e6c7456072c7887a6\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-safe-area-context:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-safe-area-context\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b0c7de0182de561e6c7456072c7887a6\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-svg::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\774a5c2fb10e94f69b9c6262ed7fae5f\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\774a5c2fb10e94f69b9c6262ed7fae5f\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-svg:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-svg\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\774a5c2fb10e94f69b9c6262ed7fae5f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-vector-icons::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a869b5a6d85696ad920375a34cdca213\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\a869b5a6d85696ad920375a34cdca213\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-vector-icons:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-vector-icons\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a869b5a6d85696ad920375a34cdca213\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-view-shot::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3b3e2f16e4a84b2cd5fc6f199827fa38\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\3b3e2f16e4a84b2cd5fc6f199827fa38\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-view-shot:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-view-shot\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3b3e2f16e4a84b2cd5fc6f199827fa38\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-webview::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\aca46405fb7b1d9f55c5ced3fb58418b\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\aca46405fb7b1d9f55c5ced3fb58418b\transformed\out\jars\libs\R.jar"
      resolved="Adtip:react-native-webview:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\aca46405fb7b1d9f55c5ced3fb58418b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:rnincallmanager::release"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\cf21143cc1f686f9d765827dcb860295\transformed\out\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\cf21143cc1f686f9d765827dcb860295\transformed\out\jars\libs\R.jar"
      resolved="Adtip:rnincallmanager:unspecified"
      partialResultsDir="F:\A1\adtip-reactnative\Adtip\node_modules\@videosdk.live\react-native-incallmanager\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\cf21143cc1f686f9d765827dcb860295\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d457dd3000de28855e41f930745b41e3\transformed\jetified-exoplayer-core-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6f38ce429bad5964b7dacb2bfaff5ba7\transformed\jetified-exoplayer-hls-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6f38ce429bad5964b7dacb2bfaff5ba7\transformed\jetified-exoplayer-hls-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3eb93ea15722ed11641782677ae1587d\transformed\jetified-exoplayer-smoothstreaming-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3eb93ea15722ed11641782677ae1587d\transformed\jetified-exoplayer-smoothstreaming-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5be09b85c83219ea331a58ecbdf31289\transformed\jetified-exoplayer-dash-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5be09b85c83219ea331a58ecbdf31289\transformed\jetified-exoplayer-dash-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.79.2:release@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b72e92ab978b0513249f06adacda07ce\transformed\jetified-hermes-android-0.79.2-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.79.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b72e92ab978b0513249f06adacda07ce\transformed\jetified-hermes-android-0.79.2-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\77b3cefe45163bc1901ca4fc5f7182cc\transformed\jetified-exoplayer-common-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:33.3.1-android@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.3.1-android\36162b3b4e9df67dae90db62a2c54616ca42c1bd\guava-33.3.1-android.jar"
      resolved="com.google.guava:guava:33.3.1-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\61c1a68c70ba9e29d9a0551de8cac3e0\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c071ca435d7e4157d1f8663601ef1e49\transformed\jetified-autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8d81b0c8ae21d76d183ae0c44210c625\transformed\jetified-fbjni-0.7.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8d81b0c8ae21d76d183ae0c44210c625\transformed\jetified-fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6de6bbbee216ff3ceb05baebaac46afa\transformed\jetified-imagepipeline-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6de6bbbee216ff3ceb05baebaac46afa\transformed\jetified-imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3e1b18841ff76bb9014d269d451a2965\transformed\jetified-memory-type-ashmem-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3e1b18841ff76bb9014d269d451a2965\transformed\jetified-memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7f7784ba77750cd53d5327a5d41c6adf\transformed\jetified-memory-type-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7f7784ba77750cd53d5327a5d41c6adf\transformed\jetified-memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ca5f86e4746e54021b6c8f6caf770c33\transformed\jetified-memory-type-java-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ca5f86e4746e54021b6c8f6caf770c33\transformed\jetified-memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\88d805db5312ff396857f098baa1c5f3\transformed\jetified-nativeimagefilters-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\88d805db5312ff396857f098baa1c5f3\transformed\jetified-nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5d258baab63e72ddb2dac3de0b2b39c1\transformed\jetified-nativeimagetranscoder-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5d258baab63e72ddb2dac3de0b2b39c1\transformed\jetified-nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\19a062cc58ca0144627e4605746e2594\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\19a062cc58ca0144627e4605746e2594\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.43.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.43.0\9425eee39e56b116d2b998b7c2cebcbd11a3c98b\checker-qual-3.43.0.jar"
      resolved="org.checkerframework:checker-qual:3.43.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.28.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.28.0\59fc00087ce372de42e394d2c789295dff2d19f0\error_prone_annotations-2.28.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.28.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\3.0.0\7399e65dd7e9ff3404f4535b2f017093bdb134c7\j2objc-annotations-3.0.0.jar"
      resolved="com.google.j2objc:j2objc-annotations:3.0.0"/>
  <library
      name="com.google.firebase:firebase-datatransport:19.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:19.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\58d10d1621a15bead637047809c31557\transformed\jetified-exoplayer-database-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\58d10d1621a15bead637047809c31557\transformed\jetified-exoplayer-database-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c710acb976df3a112d2495dd3babab5f\transformed\jetified-exoplayer-datasource-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c710acb976df3a112d2495dd3babab5f\transformed\jetified-exoplayer-datasource-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\abbc9aa494560d735d534cf0f1d538fa\transformed\jetified-exoplayer-decoder-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\abbc9aa494560d735d534cf0f1d538fa\transformed\jetified-exoplayer-decoder-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2d709fdbd7efd07e1a499a2c0b849388\transformed\jetified-exoplayer-extractor-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2d709fdbd7efd07e1a499a2c0b849388\transformed\jetified-exoplayer-extractor-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a2c3d4a381cb102ec4bb8a1df13809bf\transformed\jetified-exoplayer-container-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-container:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a2c3d4a381cb102ec4bb8a1df13809bf\transformed\jetified-exoplayer-container-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\554c3e4f50d330df6656d384faa80e8e\transformed\jetified-exoplayer-rtsp-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.19.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\554c3e4f50d330df6656d384faa80e8e\transformed\jetified-exoplayer-rtsp-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.banketree:AndroidLame-kotlin:v0.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1\jars\classes.jar"
      resolved="com.github.banketree:AndroidLame-kotlin:v0.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\43ca8b91167cc3f114f1e31d2dfcc3fd\transformed\jetified-AndroidLame-kotlin-v0.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:checkout:1.6.41@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3561dfd431ca9300f3ccd6ca17af54f0\transformed\jetified-checkout-1.6.41\jars\classes.jar"
      resolved="com.razorpay:checkout:1.6.41"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3561dfd431ca9300f3ccd6ca17af54f0\transformed\jetified-checkout-1.6.41"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dd11809084d63593099820b405d61701\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:standard-core:1.6.53@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\jars\classes.jar"
      resolved="com.razorpay:standard-core:1.6.53"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.pubscale.caterpillar:analytics:0.23@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23\jars\classes.jar"
      resolved="com.pubscale.caterpillar:analytics:0.23"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6da9fcb4b3cdec330f337d527696430e\transformed\jetified-analytics-0.23"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.10.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.10.0\ae7524eec42d4ab0c3a7cb93da010cf9bcc5007c\logging-interceptor-4.10.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.10.0"/>
  <library
      name="com.google.firebase:firebase-crashlytics-ndk:19.4.4@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-crashlytics-ndk:19.4.4"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-crashlytics:19.4.4@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-crashlytics:19.4.4"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.2.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database:21.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-database:21.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.4@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.4"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-storage:21.0.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-storage:21.0.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-sessions:2.1.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-sessions:2.1.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck:18.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck:18.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.14.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.14.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.yalantis:ucrop:2.2.10@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10\jars\classes.jar"
      resolved="com.github.yalantis:ucrop:2.2.10"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b7e10b62d187296d864392ee64da0a65\transformed\jetified-ucrop-2.2.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d5f46625db057ef05d0109c6c5ff3a42\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d5f46625db057ef05d0109c6c5ff3a42\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4083f4489864320fdd6b580d3f6bdd3a\transformed\jetified-media3-exoplayer-dash-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4083f4489864320fdd6b580d3f6bdd3a\transformed\jetified-media3-exoplayer-dash-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\fbfcdd725bcf8ac85a7f3f18643d8d95\transformed\jetified-media3-exoplayer-hls-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\fbfcdd725bcf8ac85a7f3f18643d8d95\transformed\jetified-media3-exoplayer-hls-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\91b5c4a59b6d6a7985119f29ba44ddae\transformed\jetified-media3-exoplayer-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-ui:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-ui:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8e1703580f38993d5096d25ba35ecdf1\transformed\jetified-media3-ui-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-session:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-session:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9eda951e918e947e7d9ddd1ba9b2c563\transformed\jetified-media3-session-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\eeb03681ea24d8a026ffa7aff759128b\transformed\jetified-media3-datasource-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\eeb03681ea24d8a026ffa7aff759128b\transformed\jetified-media3-datasource-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\488a6e4d60349704b6d07fc668295e48\transformed\jetified-media3-extractor-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\488a6e4d60349704b6d07fc668295e48\transformed\jetified-media3-extractor-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\bb0b45adba77a142e3e783f3ceff745b\transformed\jetified-media3-container-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\bb0b45adba77a142e3e783f3ceff745b\transformed\jetified-media3-container-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\94b244858ed62de794efeebfb057c98f\transformed\jetified-media3-decoder-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\94b244858ed62de794efeebfb057c98f\transformed\jetified-media3-decoder-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e11726acf37436fbf37d85e255145a47\transformed\jetified-media3-database-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e11726acf37436fbf37d85e255145a47\transformed\jetified-media3-database-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3109e248783e3dafcbffb05f3029995a\transformed\jetified-media3-common-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource-okhttp:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\bcb5d69659160e5a82b59d2393038c0d\transformed\jetified-media3-datasource-okhttp-1.4.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource-okhttp:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\bcb5d69659160e5a82b59d2393038c0d\transformed\jetified-media3-datasource-okhttp-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"/>
  <library
      name="com.github.mrmike:ok2curl:0.8.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.github.mrmike\ok2curl\0.8.0\29303c5f17ae419e61e57b67c69efd844669ff7a\ok2curl-0.8.0.jar"
      resolved="com.github.mrmike:ok2curl:0.8.0"/>
  <library
      name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\27abbcfab09739838c65ee0a05264ec1\transformed\jetified-barcode-scanning-common-17.0.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning-common:17.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\27abbcfab09739838c65ee0a05264ec1\transformed\jetified-barcode-scanning-common-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-common:17.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-common:17.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:common:18.11.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\jars\classes.jar"
      resolved="com.google.mlkit:common:18.11.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-config-interop:16.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9b176b15869f40e453b9776d3fced4c1\transformed\jetified-firebase-config-interop-16.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-config-interop:16.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9b176b15869f40e453b9776d3fced4c1\transformed\jetified-firebase-config-interop-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.8.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.8.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6d25c683ba2a5f4e8fc44079c57ede50\transformed\work-runtime-ktx-2.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:24.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:24.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c9fc1356237c73a36541687fd82ee2b5\transformed\jetified-play-services-ads-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-api:24.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-api:24.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.8.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.8.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\bf21f91f640e4132fe219ebff54b55a7\transformed\jetified-room-ktx-2.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.2\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.2.jar"
      resolved="androidx.room:room-common:2.5.2"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\dfbd6dfbd7eb9930845c16c988ee8525\transformed\jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\760dd814d105a65423f5f86291fd3871\transformed\jetified-googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\760dd814d105a65423f5f86291fd3871\transformed\jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.5.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.5.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2442db8ea23b05bae961f29ce182b42a\transformed\jetified-lottie-6.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5a16c5c2c9cdea968e707ccfe0d81978\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\a97a0244ead91994bae9d4a822ce48b2\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\edf5a19ba2adf1c85091121ced6da3d9\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-extensions:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-extensions:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-video:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-video:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6177f3ee616f58e544f5843e29524985\transformed\jetified-camera-video-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-lifecycle:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-lifecycle:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f46551b3f95a282437058c04b2b8602c\transformed\jetified-camera-lifecycle-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-camera2:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-camera2:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-core:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-core:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\cab56218d538a3aaadf18b40d63683ba\transformed\jetified-camera-core-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-view:1.5.0-alpha03@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03\jars\classes.jar"
      resolved="androidx.camera:camera-view:1.5.0-alpha03"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4115c2750b5ddc08fc546f1d52e12846\transformed\jetified-camera-view-1.5.0-alpha03"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\951972483b6ed7761be36fb1794da2fa\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\48c9b964a381869134ea34daa4e1cac2\transformed\recyclerview-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\37f3afb509584664f144d26e9d46905a\transformed\jetified-viewpager2-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:avif-integration:4.14.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\05085a2ee4d2268e47829dc5e7b8281e\transformed\jetified-avif-integration-4.14.2\jars\classes.jar"
      resolved="com.github.bumptech.glide:avif-integration:4.14.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\05085a2ee4d2268e47829dc5e7b8281e\transformed\jetified-avif-integration-4.14.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.zjupure:webpdecoder:********.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-********.2\jars\classes.jar"
      resolved="com.github.zjupure:webpdecoder:********.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-********.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.15.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.15.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\77a05faf56ea8351de692b50b3edb09d\transformed\jetified-glide-4.15.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-location:18.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-location:18.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\317bf0d4f174f8d0dff9db649b3a9702\transformed\jetified-play-services-location-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4842c59922b24bb6c3a519aeb184fc80\transformed\jetified-play-services-appset-16.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3b81b024f11b3ce84f83dcb40b467e5a\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3b81b024f11b3ce84f83dcb40b467e5a\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\e840d8bdf20cb87b791e7b0dce365361\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\e840d8bdf20cb87b791e7b0dce365361\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-wallet:18.1.3@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3\jars\classes.jar"
      resolved="com.google.android.gms:play-services-wallet:18.1.3"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\6a4b12df2937b548059e098326cd7bcc\transformed\jetified-play-services-wallet-18.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7aeb02bf2e195ef28c81edd39b5f3a89\transformed\jetified-firebase-appcheck-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\89202165de2bbd15e33f0d300b6ae279\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\89202165de2bbd15e33f0d300b6ae279\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-identity:17.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-identity:17.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4515e091d9c4918d4425c0b4fadad9ac\transformed\jetified-play-services-identity-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-maps:17.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-maps:17.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b9ae5d0ed3359da3c0f87e5da630cba1\transformed\jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\adacd7b5e431fdb22d892d3f47577616\transformed\jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\2dd03a2da4e4e80325824a48077809a5\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-interfaces:16.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\ec7ee6e2417445f83061705c82beb03c\transformed\jetified-vision-interfaces-16.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-interfaces:16.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\ec7ee6e2417445f83061705c82beb03c\transformed\jetified-vision-interfaces-16.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.6.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\23055739e0573ccfe469dc5146f384c3\transformed\jetified-recaptcha-18.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\4cb91d9788ca06ebac39fd309e1fab6f\transformed\jetified-integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\efc34c31fe060b184c0432cf507aff39\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\29570d6eb73b370021b74ccbfce09510\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\72c76177b46596e17e713cd9f3d03585\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\72c76177b46596e17e713cd9f3d03585\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7ca1dc3deb8725c12fb6d8854d2d457f\transformed\jetified-emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\jars\classes.jar;F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\971aefb1c45b63a51a59784e2f6f0f2e\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.ads:ads-identifier:1.0.0-alpha05@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05\jars\classes.jar"
      resolved="androidx.ads:ads-identifier:1.0.0-alpha05"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7254cf9bcab09a1b2621dc638a7e5871\transformed\jetified-ads-identifier-1.0.0-alpha05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.11.0-alpha02@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.11.0-alpha02"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\7c0a781ade5f7759726dec1a0903c6ea\transformed\webkit-1.11.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0cb78797bbebcc5583f5b3648f5d6ccd\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8f1f234df7a9575b3126ff6d50fec9a0\transformed\jetified-urimod-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8f1f234df7a9575b3126ff6d50fec9a0\transformed\jetified-urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d1239945d638394f2fa2ea034d9887f7\transformed\jetified-vito-source-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d1239945d638394f2fa2ea034d9887f7\transformed\jetified-vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f1896cdd5e4668db8d8f93d0a62d2c59\transformed\jetified-soloader-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f1896cdd5e4668db8d8f93d0a62d2c59\transformed\jetified-soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d90ca647b7e079a9cca20ce5e0695cd1\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\facb7e7221e6d5866717d8be3b94d9bd\transformed\jetified-lifecycle-service-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d75396e545ff5e8b6de70384498a4e0f\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\81895437ced6c29dbb31511b38e4b8b0\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.7@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.7"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\71f204fe940be86bf3b1a03a3572f444\transformed\lifecycle-livedata-2.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-places-placereport:17.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\59e448b4935c4e36590123a745093a7c\transformed\jetified-play-services-places-placereport-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5e9e3cfd885c9b0e37a3b851bad46214\transformed\jetified-fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\657e3daae72dd7874f0b043c60c6105e\transformed\jetified-activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\96ea896024c6120d1a1a85dce56aede2\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.0.21\8947a70750a8552acfe84ad5703e16bf072d9368\kotlin-parcelize-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8ad6a5f68ee830401b49f7ec6ef6ee94\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\939b3454ba58eaee81c8299edbbed6b3\transformed\jetified-tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.0.21\ec0f8b769af81cd91a76b530399684a9567bdc35\kotlin-android-extensions-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.github.Dimezis:BlurView:version-2.0.4@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4\jars\classes.jar"
      resolved="com.github.Dimezis:BlurView:version-2.0.4"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\c769ee9e6d014a389db47ef5f7f593e0\transformed\jetified-BlurView-version-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.4.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.4.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\169bb1fa3073498b9d2ca913ede4cd56\transformed\exifinterface-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.3.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\fa89a3c9324ea04452492f3cae338a2f\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.15.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.15.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\526cbaa91020faaa599603e1c4a03d21\transformed\jetified-gifdecoder-4.15.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.ads:ads-identifier-common:1.0.0-alpha05@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05\jars\classes.jar"
      resolved="androidx.ads:ads-identifier-common:1.0.0-alpha05"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\eb805fa6f99a9860490971d2cec19714\transformed\jetified-ads-identifier-common-1.0.0-alpha05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\5a83ea50fb5dd787e1c6cb86ed8f32c0\transformed\jetified-transport-api-3.2.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\5a83ea50fb5dd787e1c6cb86ed8f32c0\transformed\jetified-transport-api-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\44856dc4c7d2bf041bb1d39c0fd95df1\transformed\sqlite-framework-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.3.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\10cb003b54b62f570d59d1e9b1a41c21\transformed\sqlite-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\b24c3eb51e3ed594d8ad409d854e8433\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="app.notifee:core:202108261754@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\jars\classes.jar"
      resolved="app.notifee:core:202108261754"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\8f67cdf4e707817a0adbf0f742d05914\transformed\jetified-grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="org.greenrobot:eventbus:3.3.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d45c05a6182a57fed57fc0d1b9576fa4\transformed\jetified-eventbus-3.3.1\jars\classes.jar"
      resolved="org.greenrobot:eventbus:3.3.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d45c05a6182a57fed57fc0d1b9576fa4\transformed\jetified-eventbus-3.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.webrtc-sdk:android:125.6422.06.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\384e72aaed998c9eedce974536e64811\transformed\jetified-android-125.6422.06.1\jars\classes.jar"
      resolved="io.github.webrtc-sdk:android:125.6422.06.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\384e72aaed998c9eedce974536e64811\transformed\jetified-android-125.6422.06.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.mp4parser:isoparser:1.9.56@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.mp4parser\isoparser\1.9.56\a89a00b0315d4a6d82f45c5be9827a13b9ca07f6\isoparser-1.9.56.jar"
      resolved="org.mp4parser:isoparser:1.9.56"/>
  <library
      name="javazoom:jlayer:1.0.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\javazoom\jlayer\1.0.1\2bfef7a5a4c9af2184ff74b460b6d7d24349b98a\jlayer-1.0.1.jar"
      resolved="javazoom:jlayer:1.0.1"/>
  <library
      name="commons-io:commons-io:2.8.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.8.0\92999e26e6534606b5678014e66948286298a35c\commons-io-2.8.0.jar"
      resolved="commons-io:commons-io:2.8.0"/>
  <library
      name="org.apache.commons:commons-lang3:3.8@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-lang3\3.8\222fc4cf714a63f27cbdafdbd863efd0d30c8a1e\commons-lang3-3.8.jar"
      resolved="org.apache.commons:commons-lang3:3.8"/>
  <library
      name="net.time4j:time4j-android:4.8-2021a@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a\jars\classes.jar"
      resolved="net.time4j:time4j-android:4.8-2021a"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\9736d425ce9396e27ee12625a07f4d37\transformed\jetified-time4j-android-4.8-2021a"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.jakewharton:process-phoenix:2.1.2@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\jars\classes.jar"
      resolved="com.jakewharton:process-phoenix:2.1.2"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.aomedia.avif.android:avif:0.9.3.a319893@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893\jars\classes.jar"
      resolved="org.aomedia.avif.android:avif:0.9.3.a319893"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\0dc490363e0aae1f70bf91d2e3e0400e\transformed\jetified-avif-0.9.3.a319893"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.greenrobot:eventbus-java:3.3.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.greenrobot\eventbus-java\3.3.1\74487b0caceca6fcd1aff45d41a9cdc6910d7f5a\eventbus-java-3.3.1.jar"
      resolved="org.greenrobot:eventbus-java:3.3.1"/>
  <library
      name="org.aspectj:aspectjrt:1.9.7@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.aspectj\aspectjrt\1.9.7\ac42a7759a685a098b182bc0a39747f32f00adb0\aspectjrt-1.9.7.jar"
      resolved="org.aspectj:aspectjrt:1.9.7"/>
  <library
      name="org.slf4j:slf4j-api:1.8.0-beta4@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\1.8.0-beta4\83b0359d847ee053d745be7ec0d8e9e8a44304b4\slf4j-api-1.8.0-beta4.jar"
      resolved="org.slf4j:slf4j-api:1.8.0-beta4"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.6.3\b88c1bb7f149f6d2cc03898359283e57b08f39cc\auto-value-annotations-1.6.3.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.6.3"/>
  <library
      name="com.google.android.odml:image:1.0.0-beta1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1\jars\classes.jar"
      resolved="com.google.android.odml:image:1.0.0-beta1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\d1206d2a6efc99807434a9fa83aff46e\transformed\jetified-image-1.0.0-beta1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.15.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.15.1\e47ade981aefb5b975382750490d195fa569bbdf\disklrucache-4.15.1.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.15.1"/>
  <library
      name="com.github.bumptech.glide:annotations:4.15.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.15.1\d3721a4986a833e4ea71126489d12e08964b6f07\annotations-4.15.1.jar"
      resolved="com.github.bumptech.glide:annotations:4.15.1"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\fbebe52bdd503d9412c761f23dc9ff9c\transformed\jetified-protolite-well-known-types-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.1"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\fbebe52bdd503d9412c761f23dc9ff9c\transformed\jetified-protolite-well-known-types-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.5\272641fe157ed7c4a22f8d4c347bcd7f6eac8887\protobuf-javalite-3.25.5.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.5"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.android:annotations:*******@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\com.google.android\annotations\*******\a1678ba907bf92691d879fef34e1a187038f9259\annotations-*******.jar"
      resolved="com.google.android:annotations:*******"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="F:\R17DevTools\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
</libraries>
